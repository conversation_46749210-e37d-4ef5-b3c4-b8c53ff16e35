<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Requests;

use App\Models\Clinic;
use App\Modules\Clinic\Enums\PracticeType;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class SpendAnalysisRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'per_page' => ['integer', 'min:1', 'max:100'],
            'page' => ['integer', 'min:1'],

            // Date filters
            'date_from' => ['date', 'before_or_equal:date_to'],
            'date_to' => ['date', 'after_or_equal:date_from'],
            'quarter' => ['integer', 'min:1', 'max:4'],
            'year' => ['integer', 'min:2020', 'max:'.(date('Y') + 1)],

            // Clinic info filters
            'fulltime_dvm_min' => ['integer', 'min:0'],
            'fulltime_dvm_max' => ['integer', 'min:0', 'gte:fulltime_dvm_min'],
            'total_exam_rooms_min' => ['integer', 'min:0'],
            'total_exam_rooms_max' => ['integer', 'min:0', 'gte:total_exam_rooms_min'],
            'practice_types' => ['array'],
            'practice_types.*' => [Rule::enum(PracticeType::class)],

            // Total spend filters
            'min_spend' => ['numeric', 'min:0'],
            'max_spend' => ['numeric', 'min:0', 'gte:min_spend'],

            // Preferred vendor % filters
            'min_preferred_vendor_percent' => ['numeric', 'min:0', 'max:100'],
            'max_preferred_vendor_percent' => ['numeric', 'min:0', 'max:100', 'gte:min_preferred_vendor_percent'],

            // Orders filters
            'min_orders' => ['integer', 'min:0'],
            'max_orders' => ['integer', 'min:0', 'gte:min_orders'],

            // Toggle filters
            'inactive_only' => ['boolean'],

            // Order by options
            'order_by' => [
                'string',
                Rule::in([
                    'name',
                    'active_users',
                    'inactive_users',
                    'notifications',
                    'total_spend',
                    'preferred_vendor_percent',
                ]),
            ],
            'direction' => ['string', Rule::in(['asc', 'desc'])],

            // Market share limits
            'distributors_limit' => ['integer', 'min:1', 'max:20'],
            'vendors_limit' => ['integer', 'min:1', 'max:20'],
            'category_vendors_limit' => ['integer', 'min:1', 'max:10'],

            // Clinic filtering
            'clinic_ids' => ['array'],
            'clinic_ids.*' => ['bail', 'uuid', function ($attribute, $value, $fail) {
                $this->validateClinicBelongsToGpo($value, $fail);
            }],
        ];
    }

    public function messages(): array
    {
        return [
            'date_from.before_or_equal' => 'Start date must be before or equal to end date.',
            'date_to.after_or_equal' => 'End date must be after or equal to start date.',
            'fulltime_dvm_max.gte' => 'Maximum DVM count must be greater than or equal to minimum.',
            'total_exam_rooms_max.gte' => 'Maximum exam rooms must be greater than or equal to minimum.',
            'max_spend.gte' => 'Maximum spend must be greater than or equal to minimum spend.',
            'max_preferred_vendor_percent.gte' => 'Maximum preferred vendor percentage must be greater than or equal to minimum.',
            'max_orders.gte' => 'Maximum orders must be greater than or equal to minimum orders.',
            'clinic_ids.*.uuid' => 'Each clinic ID must be a valid UUID.',
            'clinic_ids.*.exists' => 'One or more clinic IDs do not exist.',
        ];
    }

    protected function prepareForValidation(): void
    {
        if (! $this->has('date_from')) {
            $this->merge(['date_from' => now()->subMonths(3)->format('Y-m-d')]);
        }

        if (! $this->has('date_to')) {
            $this->merge(['date_to' => now()->format('Y-m-d')]);
        }

        // Convert string boolean values to actual booleans for inactive_only
        if ($this->has('inactive_only')) {
            $value = $this->get('inactive_only');
            if (is_string($value)) {
                $boolValue = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                if ($boolValue !== null) {
                    $this->merge(['inactive_only' => $boolValue]);
                }
            }
        }
    }

    /**
     * Validate that a clinic belongs to the authenticated GPO
     */
    private function validateClinicBelongsToGpo(string $clinicId, Closure $fail): void
    {
        $user = $this->user('gpo');

        if (! $user || ! $user->account) {
            $fail('Unable to validate clinic ownership.');

            return;
        }

        $gpoAccountId = $user->account->id;

        $clinicBelongsToGpo = Clinic::query()
            ->whereHas('account', function ($query) use ($gpoAccountId) {
                $query->where('gpo_account_id', $gpoAccountId)
                    ->where('type', 'App\\Modules\\Account\\Models\\ClinicAccount');
            })
            ->where('id', $clinicId)
            ->exists();

        if (! $clinicBelongsToGpo) {
            $fail('The clinic does not belong to your GPO.');
        }
    }
}
