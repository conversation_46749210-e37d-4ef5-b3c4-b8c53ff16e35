<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

abstract class BaseMoniteCommand extends Command
{
    public function __construct(
        protected readonly MoniteFeatureFlagService $featureFlagService
    ) {
        parent::__construct();
    }

    /**
     * Handle the command execution with common routing logic.
     */
    public function handle(): int
    {
        $clinicId = $this->option('clinic-id');

        if ($clinicId) {
            return $this->handleSingleClinic($clinicId);
        }

        return $this->handleAllClinics();
    }

    /**
     * Handle processing for a single clinic.
     */
    protected function handleSingleClinic(string $clinicId): int
    {
        $clinic = $this->findClinic($clinicId);
        if (!$clinic) {
            return 1;
        }

        $validationResult = $this->validateClinic($clinic);
        if ($validationResult !== 0) {
            return $validationResult;
        }

        $this->processClinic($clinic);
        $this->info("{$this->getActionName()} completed for clinic: {$clinic->name}");

        return 0;
    }

    /**
     * Handle processing for all clinics.
     */
    protected function handleAllClinics(): int
    {
        $clinics = $this->getClinicsWithMonite();

        if ($clinics->isEmpty()) {
            $this->info('No clinics with Monite entities found.');
            return 0;
        }

        $this->info("{$this->getActionName()} for {$clinics->count()} clinic(s)...");

        $totalProcessed = 0;
        foreach ($clinics as $clinic) {
            if ($this->featureFlagService->isEnabled($clinic)) {
                $processed = $this->processClinic($clinic);
                $totalProcessed += $processed;
            } else {
                $this->warn("Monite disabled for clinic {$clinic->name}. Skipping.");
            }
        }

        $this->info("{$this->getActionName()} completed. Total processed: {$totalProcessed}");
        return 0;
    }

    /**
     * Find a clinic by ID and handle error cases.
     */
    protected function findClinic(string $clinicId): ?Clinic
    {
        try {
            $clinic = Clinic::find($clinicId);

            if (!$clinic) {
                $this->error("Clinic with ID {$clinicId} not found.");
                return null;
            }

            return $clinic;
        } catch (\Throwable $e) {
            $this->error("Invalid clinic ID format: {$clinicId}");
            return null;
        }
    }

    /**
     * Validate clinic for Monite operations.
     */
    protected function validateClinic(Clinic $clinic): int
    {
        if (!$this->featureFlagService->isEnabled($clinic)) {
            $this->warn("Monite disabled for clinic {$clinic->name}. Skipping.");
            return 0;
        }

        if ($this->requiresEntityId() && !$clinic->monite_entity_id) {
            $this->error("Clinic {$clinic->name} has no Monite entity ID.");
            return 1;
        }

        return 0;
    }

    /**
     * Get all clinics that have Monite entities.
     */
    protected function getClinicsWithMonite(): Collection
    {
        return Clinic::whereNotNull('monite_entity_id')->get();
    }

    /**
     * Process a single clinic - must be implemented by subclasses.
     */
    abstract protected function processClinic(Clinic $clinic): int;

    /**
     * Get the action name for display purposes.
     */
    abstract protected function getActionName(): string;

    /**
     * Whether this command requires a Monite entity ID.
     */
    protected function requiresEntityId(): bool
    {
        return true;
    }
}
