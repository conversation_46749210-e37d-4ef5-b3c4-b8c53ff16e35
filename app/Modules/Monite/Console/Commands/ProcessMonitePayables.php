<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use App\Modules\Monite\Services\MonitePayableService;
use Illuminate\Support\Facades\Log;

final class ProcessMonitePayables extends BaseMoniteCommand
{
    protected $signature = 'monite:process-payables {--clinic-id= : Process invoices for a specific clinic}';

    protected $description = 'Process invoices and create payables in Monite';

    public function __construct(
        MoniteFeatureFlagService $featureFlagService,
        private readonly MonitePayableService $payableService
    ) {
        parent::__construct($featureFlagService);
    }

    protected function processClinic(Clinic $clinic): int
    {
        try {
            $processed = $this->payableService->processClinicInvoices($clinic);
            $this->line("Processed {$processed} invoices for: {$clinic->name}");
            return $processed;
        } catch (\Throwable $e) {
            $this->error("Error processing invoices for clinic {$clinic->name}: {$e->getMessage()}");
            Log::error('Monite payable processing error', [
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
            ]);
            return 0;
        }
    }

    protected function getActionName(): string
    {
        return 'Payable processing';
    }
}
