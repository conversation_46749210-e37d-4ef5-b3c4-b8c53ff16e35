<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\SyncMissingMoniteEntitiesJob;
use App\Modules\Monite\Services\MoniteSyncService;
use Illuminate\Console\Command;

final class SyncMissingMoniteEntities extends Command
{
    protected $signature = 'monite:sync-missing 
                            {--clinic-id= : Sync entities for a specific clinic} 
                            {--queue : Use queue for job processing}
                            {--dry-run : Show what would be synced without making changes}
                            {--status : Show sync status summary only}';

    protected $description = 'Sync and fix missing Monite entities, roles, and users for clinics';

    public function __construct(
        private readonly MoniteSyncService $syncService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        if ($this->option('status')) {
            return $this->showStatusSummary();
        }

        if ($this->option('dry-run')) {
            return $this->performDryRun();
        }

        $clinicId = $this->option('clinic-id');
        $useQueue = $this->option('queue');

        if ($clinicId) {
            return $this->syncSpecificClinic($clinicId, $useQueue);
        }

        return $this->syncAllClinics($useQueue);
    }

    private function showStatusSummary(): int
    {
        $this->info('Monite Sync Status Summary');
        $this->line('');

        $summary = $this->syncService->getSyncStatusSummary();

        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Clinics', $summary['total_clinics']],
                ['Clinics with Monite Entities', $summary['clinics_with_monite_entities']],
                ['Clinics Needing Entity Setup', $summary['clinics_needing_entity_setup']],
                ['Clinics Needing User Sync', $summary['clinics_needing_user_sync']],
                ['Clinics Needing Role Sync', $summary['clinics_needing_role_sync']],
                ['Users Without Monite Mapping', $summary['total_users_without_monite_mapping']],
                ['Roles Without Monite Mapping', $summary['total_roles_without_monite_mapping']],
            ]
        );

        $this->line('');
        
        if ($summary['clinics_needing_entity_setup'] > 0 || 
            $summary['clinics_needing_user_sync'] > 0 || 
            $summary['clinics_needing_role_sync'] > 0) {
            $this->warn('Some clinics need Monite sync. Run without --status to sync them.');
        } else {
            $this->info('All clinics are up to date with Monite!');
        }

        return 0;
    }

    private function performDryRun(): int
    {
        $this->info('Monite Sync Dry Run - What would be synced:');
        $this->line('');

        // Show clinics needing entity setup
        $clinicsNeedingSetup = $this->syncService->getClinicsNeedingEntitySetup();
        if ($clinicsNeedingSetup->isNotEmpty()) {
            $this->warn("Clinics needing entity setup ({$clinicsNeedingSetup->count()}):");
            foreach ($clinicsNeedingSetup as $clinic) {
                $this->line("  - {$clinic->name} (ID: {$clinic->id})");
            }
            $this->line('');
        }

        // Show clinics needing user sync
        $clinicsNeedingUserSync = $this->syncService->getClinicsNeedingUserSync();
        if ($clinicsNeedingUserSync->isNotEmpty()) {
            $this->warn("Clinics needing user sync ({$clinicsNeedingUserSync->count()}):");
            foreach ($clinicsNeedingUserSync as $clinic) {
                $usersNeedingSync = $this->syncService->getUsersNeedingMoniteMapping($clinic);
                $this->line("  - {$clinic->name} (ID: {$clinic->id}) - {$usersNeedingSync->count()} users need sync");
            }
            $this->line('');
        }

        // Show clinics needing role sync
        $clinicsNeedingRoleSync = $this->syncService->getClinicsNeedingRoleSync();
        if ($clinicsNeedingRoleSync->isNotEmpty()) {
            $this->warn("Clinics needing role sync ({$clinicsNeedingRoleSync->count()}):");
            foreach ($clinicsNeedingRoleSync as $clinic) {
                $rolesNeedingSync = $this->syncService->getRolesNeedingMoniteMapping($clinic);
                $this->line("  - {$clinic->name} (ID: {$clinic->id}) - {$rolesNeedingSync->count()} roles need sync");
            }
            $this->line('');
        }

        if ($clinicsNeedingSetup->isEmpty() && 
            $clinicsNeedingUserSync->isEmpty() && 
            $clinicsNeedingRoleSync->isEmpty()) {
            $this->info('No clinics need Monite sync!');
        }

        return 0;
    }

    private function syncSpecificClinic(string $clinicId, bool $useQueue): int
    {
        $clinic = Clinic::find($clinicId);

        if (! $clinic) {
            $this->error("Clinic with ID {$clinicId} not found.");
            return 1;
        }

        $this->info("Syncing Monite entities for clinic: {$clinic->name} (ID: {$clinic->id})");

        if ($useQueue) {
            SyncMissingMoniteEntitiesJob::dispatch($clinic);
            $this->info("Sync job queued for clinic: {$clinic->name}");
        } else {
            $results = $this->syncService->syncClinic($clinic);
            $this->displayClinicResults($results);
        }

        return 0;
    }

    private function syncAllClinics(bool $useQueue): int
    {
        $this->info('Syncing Monite entities for all clinics...');

        if ($useQueue) {
            // Queue the job for all clinics
            SyncMissingMoniteEntitiesJob::dispatch();
            $this->info('Sync job queued for all clinics');
        } else {
            $results = $this->syncService->syncAllClinics();
            $this->displayAllResults($results);
        }

        return 0;
    }

    private function displayClinicResults(array $results): void
    {
        $this->line('');
        $this->info("Sync results for {$results['clinic_name']}:");

        if ($results['entity_created']) {
            $this->info('  ✓ Monite entity created');
        }

        if ($results['entity_updated']) {
            $this->info('  ✓ Monite entity updated');
        }

        if ($results['users_synced'] > 0) {
            $this->info("  ✓ {$results['users_synced']} users synced");
        }

        if ($results['roles_synced'] > 0) {
            $this->info("  ✓ {$results['roles_synced']} roles synced");
        }

        if (! empty($results['errors'])) {
            $this->error('  Errors:');
            foreach ($results['errors'] as $error) {
                $this->error("    - {$error}");
            }
        }

        if (empty($results['errors']) && 
            ! $results['entity_created'] && 
            ! $results['entity_updated'] && 
            $results['users_synced'] === 0 && 
            $results['roles_synced'] === 0) {
            $this->info('  ✓ Clinic is already up to date');
        }
    }

    private function displayAllResults(array $results): void
    {
        $this->line('');
        $this->info('Sync completed for all clinics:');
        $this->line("  Total clinics processed: {$results['total_processed']}");
        $this->line("  Entities created: {$results['entities_created']}");
        $this->line("  Entities updated: {$results['entities_updated']}");
        $this->line("  Users synced: {$results['users_synced']}");
        $this->line("  Roles synced: {$results['roles_synced']}");

        if (! empty($results['errors'])) {
            $this->error("  Errors encountered: " . count($results['errors']));
            foreach ($results['errors'] as $error) {
                $this->error("    - {$error}");
            }
        }

        $this->line('');
        
        // Show individual clinic results
        foreach ($results['clinic_results'] as $clinicResult) {
            $this->displayClinicResults($clinicResult);
        }
    }
}
