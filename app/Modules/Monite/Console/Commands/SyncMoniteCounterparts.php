<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\SyncAllVendorCounterpartsJob;
use App\Modules\Monite\Services\MoniteCounterpartService;
use App\Modules\Monite\Services\MoniteFeatureFlagService;

final class SyncMoniteCounterparts extends BaseMoniteCommand
{
    protected $signature = 'monite:sync-counterparts {--clinic-id= : Process vendors for a specific clinic} {--queue : Process vendors in the background}';

    protected $description = 'Sync vendors to Monite counterparts';

    public function __construct(
        MoniteFeatureFlagService $featureFlagService,
        private readonly MoniteCounterpartService $counterpartService
    ) {
        parent::__construct($featureFlagService);
    }

    protected function processClinic(Clinic $clinic): int
    {
        $useQueue = $this->option('queue');

        if ($useQueue) {
            SyncAllVendorCounterpartsJob::dispatch($clinic);
            $this->line("Counterpart sync queued for: {$clinic->name}");
            return 1; // Return count of queued jobs
        } else {
            $count = $this->counterpartService->syncAllVendorsForClinic($clinic);
            $this->line("Synced {$count} vendors for: {$clinic->name}");
            return $count; // Return count of synced vendors
        }
    }

    protected function getActionName(): string
    {
        return 'Counterpart sync';
    }
}
