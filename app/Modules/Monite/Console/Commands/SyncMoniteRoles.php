<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Jobs\SyncMoniteRolesJob;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Support\Facades\Log;

final class SyncMoniteRoles extends BaseMoniteCommand
{
    protected $signature = 'monite:sync-roles {--clinic-id= : Sync roles for specific clinic} {--queue : Process roles in queue}';

    protected $description = 'Sync user roles with Monite entity roles';

    public function __construct(
        MoniteFeatureFlagService $featureFlagService
    ) {
        parent::__construct($featureFlagService);
    }

    protected function processClinic(Clinic $clinic): int
    {
        $useQueue = $this->option('queue');

        try {
            if ($useQueue) {
                SyncMoniteRolesJob::dispatch($clinic);
                $this->line("Role sync queued for: {$clinic->name}");
                return 1;
            } else {
                $job = new SyncMoniteRolesJob($clinic);
                $action = app(\App\Modules\Monite\Actions\SyncRolesAction::class);
                $job->handle($action);
                $this->line("Roles synced for: {$clinic->name}");
                return 1;
            }
        } catch (MoniteApiException $e) {
            $this->error("Error syncing roles for {$clinic->name}: {$e->getMessage()}");
            Log::error('Role sync error', [
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
                'response' => $e->response?->json(),
            ]);
            return 0;
        } catch (\Throwable $e) {
            $this->error("Unexpected error syncing roles for {$clinic->name}: {$e->getMessage()}");
            Log::error('Unexpected role sync error', [
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
            ]);
            return 0;
        }
    }

    protected function getActionName(): string
    {
        return 'Role sync';
    }
}
