<?php

declare(strict_types=1);

namespace App\Modules\Monite\Jobs;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteSyncService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class SyncMissingMoniteEntitiesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private readonly ?Clinic $clinic = null
    ) {}

    public function handle(MoniteSyncService $syncService): void
    {
        if ($this->clinic) {
            $this->syncSpecificClinic($syncService);
        } else {
            $this->syncAllClinics($syncService);
        }
    }

    private function syncSpecificClinic(MoniteSyncService $syncService): void
    {
        Log::info('Starting Monite sync for specific clinic', [
            'clinic_id' => $this->clinic->id,
            'clinic_name' => $this->clinic->name,
        ]);

        try {
            $results = $syncService->syncClinic($this->clinic);
            
            Log::info('Monite sync completed for specific clinic', [
                'clinic_id' => $this->clinic->id,
                'clinic_name' => $this->clinic->name,
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            Log::error('Monite sync failed for specific clinic', [
                'clinic_id' => $this->clinic->id,
                'clinic_name' => $this->clinic->name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    private function syncAllClinics(MoniteSyncService $syncService): void
    {
        Log::info('Starting Monite sync for all clinics');

        try {
            $results = $syncService->syncAllClinics();
            
            Log::info('Monite sync completed for all clinics', [
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            Log::error('Monite sync failed for all clinics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    public function getClinic(): ?Clinic
    {
        return $this->clinic;
    }
}
