# Monite Integration Module

Laravel module for integrating with the Monite API for financial management in veterinary clinics.

## 🎯 What It Does

- **Entity Management**: Create/manage Monite entities for clinics
- **User Sync**: Sync clinic users with Monite
- **Role Mapping**: Map clinic roles to Monite permissions
- **Vendor Sync**: Sync vendors to Monite counterparts
- **Payables**: Convert invoices to Monite payables
- **Feature Flags**: Control Monite features per clinic
- **Token Management**: OAuth2 token handling with caching
- **Error Handling**: Comprehensive error handling and logging

## ⚙️ Setup

Add to `.env`:
```env
MONITE_ENVIRONMENT=sandbox
MONITE_CLIENT_ID=your_client_id
MONITE_CLIENT_SECRET=your_client_secret
```

## 🖥️ Artisan Commands

### `monite:sync-missing`
Comprehensive sync command for entities, roles, and users.

```bash
# Check sync status
php artisan monite:sync-missing --status

# Sync all missing entities/users
php artisan monite:sync-missing

# Sync specific clinic
php artisan monite:sync-missing --clinic-id=123

# Preview changes (dry run)
php artisan monite:sync-missing --dry-run

# Use queue for processing
php artisan monite:sync-missing --queue
```

### `monite:sync-users`
Sync clinic users with Monite entity users.

```bash
# Sync users for all clinics
php artisan monite:sync-users

# Sync users for specific clinic
php artisan monite:sync-users --clinic-id=123

# Use queue for processing
php artisan monite:sync-users --queue
```

### `monite:sync-roles`
Sync clinic roles with Monite entity roles.

```bash
# Sync roles for all clinics
php artisan monite:sync-roles

# Sync roles for specific clinic
php artisan monite:sync-roles --clinic-id=123

# Use queue for processing
php artisan monite:sync-roles --queue
```

### `monite:sync-counterparts`
Sync vendors to Monite counterparts.

```bash
# Sync counterparts for all clinics
php artisan monite:sync-counterparts

# Sync counterparts for specific clinic
php artisan monite:sync-counterparts --clinic-id=123

# Use queue for processing
php artisan monite:sync-counterparts --queue
```

### `monite:process-payables`
Convert invoices to Monite payables.

```bash
# Process payables for all clinics
php artisan monite:process-payables

# Process payables for specific clinic
php artisan monite:process-payables --clinic-id=123
```

**Common Options:**
- `--clinic-id=ID` - Process for specific clinic
- `--queue` - Use background queue processing

## 🏗️ Architecture

### Base Command Class
All Monite commands extend `BaseMoniteCommand` which provides:
- Common clinic validation logic
- Feature flag checking
- Error handling patterns
- Consistent output formatting

### Service Layer
- **MoniteApiClient**: HTTP client for Monite API
- **MoniteTokenManager**: OAuth2 token management
- **MoniteSyncService**: Main sync orchestration
- **MoniteEntityService**: Entity creation/management
- **MoniteUserService**: User sync operations
- **MoniteRoleService**: Role sync operations
- **MoniteCounterpartService**: Vendor sync operations
- **MonitePayableService**: Invoice to payable conversion
- **MoniteFeatureFlagService**: Feature flag management

### Action Layer
- **CreateOrUpdateEntityAction**: Entity creation logic
- **SyncUsersAction**: User sync logic
- **SyncRolesAction**: Role sync logic
- **CreatePayableFromInvoiceAction**: Payable creation logic
- **DeleteEntityAction**: Entity deletion logic

### Job Layer
All operations can be queued for background processing:
- **SyncMissingMoniteEntitiesJob**: Complete sync job
- **SyncMoniteUsersJob**: User sync job
- **SyncMoniteRolesJob**: Role sync job
- **SyncAllVendorCounterpartsJob**: Vendor sync job
- **CreateOrUpdateMoniteEntityJob**: Entity creation job
- **CreatePayableFromInvoiceJob**: Payable creation job
- **DeleteMoniteEntityJob**: Entity deletion job

## 🔧 Key Services

### `MoniteSyncService`
Main service for syncing clinics with Monite:
```php
$syncService = app(MoniteSyncService::class);
$results = $syncService->syncClinic($clinic);
```

### `MoniteFeatureFlagService`
Control Monite features per clinic:
```php
$featureService = app(MoniteFeatureFlagService::class);
$featureService->enable($clinic);
$featureService->isEnabled($clinic);
```

### `MoniteApiClient`
HTTP client with retry logic and error handling:
```php
$client = app(MoniteApiClientInterface::class);
$response = $client->get('/entities');
```

### `MoniteTokenManager`
OAuth2 token management with caching:
```php
$tokenManager = app(MoniteTokenManager::class);
$token = $tokenManager->getAccessToken();
```

## 🗄️ Models

### `MoniteUserMapping`
Maps local users to Monite users:
```php
// Fields: clinic_id, user_id, monite_user_id
$mapping = MoniteUserMapping::where('clinic_id', $clinic->id)
    ->where('user_id', $user->id)
    ->first();
```

### `MoniteRoleMapping`
Maps local roles to Monite roles:
```php
// Fields: clinic_id, role_id, monite_role_id
$mapping = MoniteRoleMapping::where('clinic_id', $clinic->id)
    ->where('role_id', $role->id)
    ->first();
```

### `MoniteVendorMapping`
Maps local vendors to Monite counterparts:
```php
// Fields: clinic_id, vendor_id, monite_counterpart_id
$mapping = MoniteVendorMapping::where('clinic_id', $clinic->id)
    ->where('vendor_id', $vendor->id)
    ->first();
```

## 🚩 Feature Flags

Control Monite per clinic using Laravel Pennant:
```php
$featureService = app(MoniteFeatureFlagService::class);

// Enable/disable for clinic
$featureService->enable($clinic);
$featureService->disable($clinic);

// Check status
$featureService->isEnabled($clinic);
```

## 🔐 Authentication & Security

### OAuth2 Flow
- **Client Credentials**: For backend-to-backend communication
- **Entity User**: For user-specific operations
- **Token Caching**: Automatic token refresh and caching
- **Rate Limiting**: Built-in rate limiting with retry logic

### Error Handling
- **MoniteApiException**: Custom exception for API errors
- **Retry Logic**: Automatic retry for transient failures
- **Logging**: Comprehensive error logging
- **Graceful Degradation**: Commands continue on individual failures

## 📊 Data Transfer Objects

### `MoniteApiResponse`
Structured response handling:
```php
$response = MoniteApiResponse::fromResponse($httpResponse);
$data = $response->getData();
$pagination = $response->getPagination();
```

## 🎛️ Configuration

Configuration in `config/monite.php`:
- **Environments**: Sandbox/Production settings
- **Credentials**: Client ID/Secret
- **API Settings**: Base URLs, version, timeouts
- **Rate Limiting**: Request limits and retry policies
- **Role Permissions**: Detailed permission mappings

## 🔄 Integration Points

### Nova Actions
- **SetupMoniteIntegration**: One-click Monite setup for clinics

### Traits
- **ChecksMoniteFeatureFlag**: Reusable feature flag checking

### Enums
- **MoniteEnvironment**: Environment management
- **MoniteGrantType**: OAuth2 grant types

## ⚠️ Troubleshooting

```bash
# Check sync status
php artisan monite:sync-missing --status

# Preview changes
php artisan monite:sync-missing --dry-run

# Check feature flags
php artisan tinker
>>> app(MoniteFeatureFlagService::class)->isEnabled($clinic)

# Check token status
>>> app(MoniteTokenManager::class)->getAccessToken()

# Test API connection
>>> app(MoniteApiClientInterface::class)->get('/entities')
```

## 🚀 Usage Examples

### Manual Entity Creation
```php
$entityService = app(MoniteEntityService::class);
$entityId = $entityService->createEntityForClinic($clinic);
```

### User Sync
```php
$userService = app(MoniteUserService::class);
$userService->syncUserForClinic($user, $clinic);
```

### Payable Processing
```php
$payableService = app(MonitePayableService::class);
$count = $payableService->processClinicInvoices($clinic);
```

### Queue Jobs
```php
// Dispatch sync job
SyncMissingMoniteEntitiesJob::dispatch($clinic);

// Dispatch user sync
SyncMoniteUsersJob::dispatch($clinic);
```

## 📈 Performance Features

- **Queue Support**: All operations can be queued
- **Batch Processing**: Efficient bulk operations
- **Pagination**: Automatic pagination handling
- **Caching**: Token caching and response caching
- **Rate Limiting**: Built-in API rate limiting
- **Retry Logic**: Automatic retry for failed requests

## 🔍 Monitoring & Logging

- **Comprehensive Logging**: All operations logged
- **Error Tracking**: Detailed error information
- **Performance Metrics**: Request timing and counts
- **Status Reporting**: Command status summaries