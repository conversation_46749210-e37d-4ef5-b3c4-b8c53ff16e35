<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Models\User;
use App\Modules\Monite\Models\MoniteRoleMapping;
use App\Modules\Monite\Models\MoniteUserMapping;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

final class MoniteSyncService
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteEntityService $entityService,
        private readonly MoniteUserService $userService,
        private readonly MoniteRoleService $roleService
    ) {}

    /**
     * Get all clinics that need Monite entity setup
     */
    public function getClinicsNeedingEntitySetup(): Collection
    {
        return Clinic::whereNull('monite_entity_id')
            ->whereHas('account.users') // Only clinics with users
            ->get()
            ->filter(fn (Clinic $clinic) => $this->isMoniteEnabledForClinic($clinic));
    }

    /**
     * Get all clinics with Monite entities that need user sync
     */
    public function getClinicsNeedingUserSync(): Collection
    {
        return Clinic::whereNotNull('monite_entity_id')
            ->whereHas('account.users') // Only clinics with users
            ->get()
            ->filter(fn (Clinic $clinic) => $this->isMoniteEnabledForClinic($clinic))
            ->filter(fn (Clinic $clinic) => $this->hasUsersWithoutMoniteMapping($clinic));
    }

    /**
     * Get all clinics with Monite entities that need role sync
     */
    public function getClinicsNeedingRoleSync(): Collection
    {
        return Clinic::whereNotNull('monite_entity_id')
            ->whereHas('account.users') // Only clinics with users
            ->get()
            ->filter(fn (Clinic $clinic) => $this->isMoniteEnabledForClinic($clinic))
            ->filter(fn (Clinic $clinic) => $this->hasRolesWithoutMoniteMapping($clinic));
    }

    /**
     * Get users for a clinic that don't have Monite mappings
     */
    public function getUsersNeedingMoniteMapping(Clinic $clinic): Collection
    {
        if (! $this->isMoniteEnabledForClinic($clinic)) {
            return collect();
        }

        $existingMappings = MoniteUserMapping::where('clinic_id', $clinic->id)
            ->pluck('user_id')
            ->toArray();

        return $clinic->account->users
            ->whereNotIn('id', $existingMappings)
            ->filter(fn (User $user) => $user->roles->isNotEmpty()); // Only users with roles
    }

    /**
     * Get roles for a clinic that don't have Monite mappings
     */
    public function getRolesNeedingMoniteMapping(Clinic $clinic): Collection
    {
        if (! $this->isMoniteEnabledForClinic($clinic)) {
            return collect();
        }

        $existingMappings = MoniteRoleMapping::where('clinic_id', $clinic->id)
            ->pluck('role_id')
            ->toArray();

        // Get all roles that are used by users in this clinic
        $usedRoles = $clinic->account->users
            ->flatMap(fn (User $user) => $user->roles)
            ->unique('id');

        return $usedRoles->whereNotIn('id', $existingMappings);
    }

    /**
     * Sync a single clinic - create entity if missing, then sync users and roles
     */
    public function syncClinic(Clinic $clinic): array
    {
        $results = [
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
            'entity_created' => false,
            'entity_updated' => false,
            'users_synced' => 0,
            'roles_synced' => 0,
            'errors' => [],
        ];

        try {
            // Check if Monite feature is enabled
            if (! $this->isMoniteEnabledForClinic($clinic)) {
                $results['errors'][] = 'Monite feature is disabled for this clinic';
                return $results;
            }

            // Create or update entity
            if (! $clinic->monite_entity_id) {
                $entityId = $this->entityService->createEntityForClinic($clinic);
                if ($entityId) {
                    $results['entity_created'] = true;
                    $clinic->refresh(); // Refresh to get the new entity_id
                } else {
                    $results['errors'][] = 'Failed to create Monite entity';
                    return $results;
                }
            } else {
                $this->entityService->updateEntityForClinic($clinic);
                $results['entity_updated'] = true;
            }

            // Sync roles first (users need roles to be created)
            $rolesNeedingSync = $this->getRolesNeedingMoniteMapping($clinic);
            foreach ($rolesNeedingSync as $role) {
                try {
                    $this->roleService->createOrUpdateRoleForClinic($role, $clinic);
                    $results['roles_synced']++;
                } catch (\Exception $e) {
                    $results['errors'][] = "Failed to sync role {$role->name}: {$e->getMessage()}";
                    Log::error('Failed to sync role for clinic', [
                        'clinic_id' => $clinic->id,
                        'role_id' => $role->id,
                        'role_name' => $role->name,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            // Sync users
            $usersNeedingSync = $this->getUsersNeedingMoniteMapping($clinic);
            foreach ($usersNeedingSync as $user) {
                try {
                    $this->userService->createOrUpdateUserForClinic($user, $clinic);
                    $results['users_synced']++;
                } catch (\Exception $e) {
                    $results['errors'][] = "Failed to sync user {$user->email}: {$e->getMessage()}";
                    Log::error('Failed to sync user for clinic', [
                        'clinic_id' => $clinic->id,
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info('Clinic sync completed', $results);

        } catch (\Exception $e) {
            $results['errors'][] = "Failed to sync clinic: {$e->getMessage()}";
            Log::error('Failed to sync clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'error' => $e->getMessage(),
            ]);
        }

        return $results;
    }

    /**
     * Sync all clinics that need Monite setup
     */
    public function syncAllClinics(): array
    {
        $results = [
            'total_processed' => 0,
            'entities_created' => 0,
            'entities_updated' => 0,
            'users_synced' => 0,
            'roles_synced' => 0,
            'errors' => [],
            'clinic_results' => [],
        ];

        // Get all clinics that need entity setup
        $clinicsNeedingSetup = $this->getClinicsNeedingEntitySetup();
        
        // Get all clinics that need user/role sync
        $clinicsNeedingUserSync = $this->getClinicsNeedingUserSync();
        $clinicsNeedingRoleSync = $this->getClinicsNeedingRoleSync();
        
        // Combine all clinics that need any kind of sync
        $allClinicsToSync = $clinicsNeedingSetup
            ->merge($clinicsNeedingUserSync)
            ->merge($clinicsNeedingRoleSync)
            ->unique('id');

        foreach ($allClinicsToSync as $clinic) {
            $clinicResult = $this->syncClinic($clinic);
            $results['clinic_results'][] = $clinicResult;
            $results['total_processed']++;
            $results['entities_created'] += $clinicResult['entity_created'] ? 1 : 0;
            $results['entities_updated'] += $clinicResult['entity_updated'] ? 1 : 0;
            $results['users_synced'] += $clinicResult['users_synced'];
            $results['roles_synced'] += $clinicResult['roles_synced'];
            $results['errors'] = array_merge($results['errors'], $clinicResult['errors']);
        }

        Log::info('All clinics sync completed', $results);

        return $results;
    }

    /**
     * Check if a clinic has users without Monite mappings
     */
    private function hasUsersWithoutMoniteMapping(Clinic $clinic): bool
    {
        return $this->getUsersNeedingMoniteMapping($clinic)->isNotEmpty();
    }

    /**
     * Check if a clinic has roles without Monite mappings
     */
    private function hasRolesWithoutMoniteMapping(Clinic $clinic): bool
    {
        return $this->getRolesNeedingMoniteMapping($clinic)->isNotEmpty();
    }

    /**
     * Get summary of sync status for all clinics
     */
    public function getSyncStatusSummary(): array
    {
        $clinicsNeedingEntitySetup = $this->getClinicsNeedingEntitySetup();
        $clinicsNeedingUserSync = $this->getClinicsNeedingUserSync();
        $clinicsNeedingRoleSync = $this->getClinicsNeedingRoleSync();

        return [
            'total_clinics' => Clinic::count(),
            'clinics_with_monite_entities' => Clinic::whereNotNull('monite_entity_id')->count(),
            'clinics_needing_entity_setup' => $clinicsNeedingEntitySetup->count(),
            'clinics_needing_user_sync' => $clinicsNeedingUserSync->count(),
            'clinics_needing_role_sync' => $clinicsNeedingRoleSync->count(),
            'total_users_without_monite_mapping' => MoniteUserMapping::count() ? 
                User::whereDoesntHave('moniteUserMappings')->count() : User::count(),
            'total_roles_without_monite_mapping' => MoniteRoleMapping::count() ? 
                \App\Modules\Account\Models\Role::whereDoesntHave('moniteRoleMappings')->count() : 
                \App\Modules\Account\Models\Role::count(),
        ];
    }
}
