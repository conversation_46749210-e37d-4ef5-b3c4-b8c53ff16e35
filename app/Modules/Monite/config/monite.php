<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Monite API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Monite API integration including environments,
    | authentication settings, and API endpoints.
    |
    */

    'environment' => env('MONITE_ENVIRONMENT', 'sandbox'),

    'environments' => [
        'sandbox' => [
            'partner_portal_url' => 'https://portal.sandbox.monite.com',
            'api_base_url' => 'https://api.sandbox.monite.com',
        ],
        'production' => [
            'partner_portal_url' => 'https://us.portal.monite.com',
            'api_base_url' => 'https://us.api.monite.com',
        ],
    ],

    'api_version' => env('MONITE_API_VERSION', '2024-05-25'),

    'credentials' => [
        'client_id' => env('MONITE_CLIENT_ID'),
        'client_secret' => env('MONITE_CLIENT_SECRET'),
    ],

    'token' => [
        'cache_key' => 'monite_access_token',
        'cache_ttl' => 1800, // 30 minutes in seconds
        'refresh_threshold' => 300, // 5 minutes before expiry
    ],

    'rate_limiting' => [
        'requests_per_minute' => env('MONITE_RATE_LIMIT_RPM', 60),
        'burst_limit' => env('MONITE_RATE_LIMIT_BURST', 100),
    ],

    'timeout' => env('MONITE_API_TIMEOUT', 30),

    'retry' => [
        'max_attempts' => env('MONITE_RETRY_MAX_ATTEMPTS', 3),
        'delay' => env('MONITE_RETRY_DELAY', 1000), // milliseconds
        'backoff_multiplier' => env('MONITE_RETRY_BACKOFF', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monite Role Permissions Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the permission configurations for different user roles
    | when creating Monite roles. Each role has specific permissions for
    | different object types and actions.
    |
    */

    'roles' => [
        'clinic:owner' => [
            'objects' => [
                [
                    'object_type' => 'approval_policy',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'approval_request',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'comment',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'counterpart',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'counterpart_vat_id',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'delivery_note',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'entity',
                    'actions' => [
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'entity_bank_account',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'entity_vat_ids',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'entity_user',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'export',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'mailbox',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'ocr_task',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'onboarding',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'overdue_reminder',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payable',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'create_from_mail', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                        ['action_name' => 'submit', 'permission' => 'allowed'],
                        ['action_name' => 'approve', 'permission' => 'allowed'],
                        ['action_name' => 'cancel', 'permission' => 'allowed'],
                        ['action_name' => 'pay', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payables_purchase_order',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payment_record',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payment_reminder',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'person',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'product',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'project',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'receipt',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'receivable',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'role',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'tag',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'transaction',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
            ],
        ],

        'clinic:admin' => [
            'objects' => [
                [
                    'object_type' => 'approval_policy',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'approval_request',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'comment',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'counterpart',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'counterpart_vat_id',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payable',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'create_from_mail', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                        ['action_name' => 'submit', 'permission' => 'allowed'],
                        ['action_name' => 'approve', 'permission' => 'allowed'],
                        ['action_name' => 'cancel', 'permission' => 'allowed'],
                        ['action_name' => 'pay', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payables_purchase_order',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payment_record',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'role',
                    'actions' => [
                        ['action_name' => 'read', 'permission' => 'allowed'],
                    ],
                ],
            ],
        ],

        'clinic:manager' => [
            'objects' => [
                [
                    'object_type' => 'approval_policy',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'approval_request',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'comment',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'counterpart',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'counterpart_vat_id',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payable',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'create_from_mail', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                        ['action_name' => 'submit', 'permission' => 'allowed'],
                        ['action_name' => 'approve', 'permission' => 'allowed'],
                        ['action_name' => 'cancel', 'permission' => 'allowed'],
                        ['action_name' => 'pay', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payables_purchase_order',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                        ['action_name' => 'update', 'permission' => 'allowed'],
                        ['action_name' => 'delete', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'payment_record',
                    'actions' => [
                        ['action_name' => 'create', 'permission' => 'allowed'],
                        ['action_name' => 'read', 'permission' => 'allowed'],
                    ],
                ],
                [
                    'object_type' => 'role',
                    'actions' => [
                        ['action_name' => 'read', 'permission' => 'allowed'],
                    ],
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Permissions
    |--------------------------------------------------------------------------
    |
    | Default permissions for roles that don't have specific configurations.
    |
    */
    'default_permissions' => [
        'objects' => [
            [
                'object_type' => 'approval_policy',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                    ['action_name' => 'update', 'permission' => 'allowed'],
                    ['action_name' => 'delete', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'approval_request',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                    ['action_name' => 'update', 'permission' => 'allowed'],
                    ['action_name' => 'delete', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'comment',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                    ['action_name' => 'update', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'counterpart',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                    ['action_name' => 'update', 'permission' => 'allowed'],
                    ['action_name' => 'delete', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'counterpart_vat_id',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                    ['action_name' => 'update', 'permission' => 'allowed'],
                    ['action_name' => 'delete', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'payable',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'create_from_mail', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                    ['action_name' => 'update', 'permission' => 'allowed'],
                    ['action_name' => 'delete', 'permission' => 'allowed'],
                    ['action_name' => 'submit', 'permission' => 'allowed'],
                    ['action_name' => 'approve', 'permission' => 'allowed'],
                    ['action_name' => 'cancel', 'permission' => 'allowed'],
                    ['action_name' => 'pay', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'payables_purchase_order',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                    ['action_name' => 'update', 'permission' => 'allowed'],
                    ['action_name' => 'delete', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'payment_record',
                'actions' => [
                    ['action_name' => 'create', 'permission' => 'allowed'],
                    ['action_name' => 'read', 'permission' => 'allowed'],
                ],
            ],
            [
                'object_type' => 'role',
                'actions' => [
                    ['action_name' => 'read', 'permission' => 'allowed'],
                ],
            ],
        ],
    ],
];
