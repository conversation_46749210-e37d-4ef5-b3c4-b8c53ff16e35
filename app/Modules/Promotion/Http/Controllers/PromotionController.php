<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Http\Controllers;

use App\Models\Clinic;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Data\PromotionData;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Queries\PromotionQuery;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

final class PromotionController
{
    /**
     * Get base promotion query with common filtering
     */
    private function getBasePromotionQuery(Clinic $clinic): Builder
    {
        $gpoId = $clinic->account?->gpo_account_id;
        $connectedVendorIds = $clinic->connectedVendors->pluck('id')->toArray();

        return Promotion::query()
            ->with(['vendor', 'productOffers.clinics', 'productOffers.product', 'productOffers.vendor', 'rules.conditions', 'rules.actions'])
            ->where('promotionable_type', GpoAccount::class)
            ->where('promotionable_id', $gpoId)
            ->active()
            ->whereIn('vendor_id', $connectedVendorIds)
            ->whereHas('productOffers', function ($query) use ($connectedVendorIds) {
                $query->whereIn('vendor_id', $connectedVendorIds)
                    ->whereColumn('product_offers.vendor_id', 'promotions.vendor_id');
            });
    }

    public function index(Request $request): JsonResponse
    {
        $clinic = $request->clinic()->load(['account.gpo', 'connectedVendors']);

        if ($clinic->account?->gpo_account_id === null) {
            return new JsonResponse([]);
        }

        $query = $this->getBasePromotionQuery($clinic);

        // Filter by type if provided
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        $promotions = PromotionQuery::for($query)->get();

        return new JsonResponse(PromotionData::collect($promotions));
    }

    public function show(Request $request, string $id): JsonResponse
    {
        $clinic = $request->clinic()->load(['account.gpo', 'connectedVendors']);

        if ($clinic->account?->gpo_account_id === null) {
            return new JsonResponse([]);
        }

        $promotion = $this->getBasePromotionQuery($clinic)
            ->where('id', $id)
            ->firstOrFail();

        return new JsonResponse(PromotionData::from($promotion));
    }
}
