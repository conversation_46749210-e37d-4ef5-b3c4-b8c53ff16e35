<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;

final class AddToCartFromSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle multiple saved item IDs.
     */
    public function handle(array $savedItemIds): Collection
    {
        // Load all saved items with their relationships
        $savedItems = SavedItem::with(['clinic.cart'])
            ->whereIn('id', $savedItemIds)
            ->get();

        $clinic = $savedItems->first()?->clinic;
        $cart = $clinic?->cart;
        if (! $cart) {
            $cart = $clinic->cart()->create();
        }

        foreach ($savedItems as $savedItem) {
            try {
                $productOffer = $savedItem->productOffer;
                $quantity = $savedItem->quantity;

                $cartItem = $cart->items()->where('product_offer_id', $productOffer->id)->first();

                if ($cartItem) {
                    $cart->updateItem($productOffer, ['quantity' => $cartItem->quantity + $quantity]);
                } else {
                    $cart->addItem($productOffer, $quantity);
                }


                $savedItem->delete();
            } catch (Exception $e) {
                Log::error('Failed to add item to saved items', [
                    'clinic_id' => $clinic->id,
                    'cart_item_id' => $cartItem->id,
                    'error' => $e->getMessage(),
                ]);
                continue;
            }
        }

        $this->getCartAction->forgetCache($clinic);

        return $savedItems;
    }
}
