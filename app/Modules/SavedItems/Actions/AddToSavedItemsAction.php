<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Models\CartItem;
use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

final class AddToSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle multiple cart item IDs.
     */
    public function handle(array $cartItemIds): Collection
    {
        $cartItems = CartItem::with(['cart.clinic'])
            ->whereIn('id', $cartItemIds)
            ->get();

        $savedItems = collect();
        $processedCartItemIds = collect();
        $clinic = $cartItems->first()?->cart->clinic;

        foreach ($cartItems as $cartItem) {
            try {
                $productOffer = $cartItem->productOffer;
                $quantity = $cartItem->quantity;

                $savedItem = SavedItem::firstOrNew([
                    'clinic_id' => $clinic->id,
                    'product_offer_id' => $productOffer->id,
                ], [
                    'quantity' => 0,
                ]);

                if ($savedItem->exists) {
                    $savedItem->quantity += $quantity;
                } else {
                    $savedItem->quantity = $quantity;
                }

                $savedItem->save();
                $cartItem->delete();
                $savedItems->push($savedItem);
            } catch (Exception $e) {
            Log::error('Failed to add item to saved items', [
                'clinic_id' => $clinic->id,
                'cart_item_id' => $cartItem->id,
                'error' => $e->getMessage(),
            ]);

            continue;
        }
        }

        // Clear cart cache to ensure fresh data is loaded
        if ($clinic) {
            $this->getCartAction->forgetCache($clinic);
        }

        return $savedItems;
    }
}
