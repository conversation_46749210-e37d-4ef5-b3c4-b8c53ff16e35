<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Controllers;

use App\Modules\SavedItems\Actions\AddToCartFromSavedItemsAction;
use App\Modules\SavedItems\Actions\AddToSavedItemsAction;
use App\Modules\SavedItems\Data\SavedItemData;
use App\Modules\SavedItems\Data\SavedItemsCollectionData;
use App\Modules\SavedItems\Http\Requests\AddToSavedItemsRequest;
use App\Modules\SavedItems\Http\Requests\GetSavedItemsRequest;
use App\Modules\SavedItems\Http\Requests\RemoveFromSavedItemsRequest;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;


final class SavedItemsController extends Controller
{
    public function __construct(
        private readonly AddToSavedItemsAction $addToSavedItemsAction,
        private readonly AddToCartFromSavedItemsAction $addToCartFromSavedItemsAction,
    ) {}

    public function addToSavedItems(AddToSavedItemsRequest $request): JsonResponse
    {
        $cartItemIds = $request->get('cart_item_ids', []);

        $savedItems = $this->addToSavedItemsAction->handle($cartItemIds);

        // Load relationships for all saved items
        $savedItemsWithRelations = $savedItems->map(function ($savedItem) {
            return $savedItem->load(['productOffer', 'productOffer.vendor']);
        });

        return new JsonResponse([
            'saved_items' => SavedItemData::collect($savedItemsWithRelations),
            'count' => $savedItems->count(),
        ]);
    }

    public function addToCartFromSavedItems(RemoveFromSavedItemsRequest $request): JsonResponse
    {
        $savedItemIds = $request->getSavedItemIds();

        $savedItems = $this->addToCartFromSavedItemsAction->handle($savedItemIds);

        // Load relationships for all saved items
        $savedItemsWithRelations = $savedItems->map(function ($savedItem) {
            return $savedItem->load(['productOffer', 'productOffer.vendor']);
        });

        return new JsonResponse([
            'saved_items' => SavedItemData::collect($savedItemsWithRelations),
            'count' => $savedItems->count(),
        ]);
    }

    public function removeFromSavedItems(RemoveFromSavedItemsRequest $request): Response
    {
        $savedItemIds = $request->get('saved_item_ids', []);

        SavedItem::whereIn('id', $savedItemIds)->delete();

        return new Response(null, 204);
    }

    public function index(GetSavedItemsRequest $request): JsonResponse
    {
        $savedItems = SavedItem::where('clinic_id', $request->clinicId())
            ->with(['productOffer', 'productOffer.vendor', 'productOffer.product'])
            ->orderBy('created_at', 'desc')
            ->get();

        return new JsonResponse(
            SavedItemsCollectionData::fromModel($savedItems)
        );
    }
}
