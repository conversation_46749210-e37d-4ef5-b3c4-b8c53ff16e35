<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Requests;

use App\Models\CartItem;
use Illuminate\Foundation\Http\FormRequest;

final class AddToSavedItemsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->account->clinics->contains(fn ($clinic) => $clinic->id === $this->clinicId());
    }

    public function rules(): array
    {
        return [
            'cart_item_ids' => 'required|array|min:1',
            'cart_item_ids.*' => 'required|string|uuid|exists:cart_items,id',
        ];
    }

    public function messages(): array
    {
        return [
            'cart_item_ids.required' => 'Cart item IDs are required.',
            'cart_item_ids.array' => 'Cart item IDs must be an array.',
            'cart_item_ids.min' => 'At least one cart item ID is required.',
            'cart_item_ids.*.required' => 'Each cart item ID is required.',
            'cart_item_ids.*.uuid' => 'Each cart item ID must be a valid UUID.',
            'cart_item_ids.*.exists' => 'One or more cart items do not exist.',
        ];
    }
}
