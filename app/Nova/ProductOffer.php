<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\ProductStockStatus;
use App\Nova\Actions\CleanUpStaleProducts;
use <PERSON><PERSON>\Nova\Fields\Badge;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Panel;

final class ProductOffer extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ProductOffer>
     */
    public static $model = \App\Models\ProductOffer::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'vendor.name',
        'vendor_sku',
    ];

    public static function label()
    {
        return 'Products';
    }

    /**
     * Determine if this resource uses Laravel Scout.
     */
    public static function usesScout(): bool
    {
        return false;
    }

    public function subtitle()
    {
        return "{$this->vendor?->name} &middot; {$this->vendor_sku}";
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [

            Text::make('Name', 'name')
                ->sortable()
                ->rules('required', 'max:255')
                ->showOnPreview(),

            Text::make('Vendor SKU', 'vendor_sku')
                ->exceptOnForms(),

            BelongsTo::make('Product', 'product', Product::class)
                ->showOnIndex(false)
                ->nullable()
                ->searchable(),

            BelongsTo::make('Vendor', 'vendor', Vendor::class)
                ->sortable()
                ->showOnPreview(),

            Currency::make('Price', 'price')
                ->currency('USD')
                ->asMinorUnits()
                ->sortable()
                ->required()
                ->min(0)
                ->step('0.01'),

            Badge::make('Stock Status', 'stock_status')
                ->exceptOnForms()
                ->map([
                    ProductStockStatus::InStock->value => 'success',
                    ProductStockStatus::DropShip->value => 'warning',
                    ProductStockStatus::SpecialOrder->value => 'warning',
                    ProductStockStatus::OutOfStock->value => 'danger',
                    ProductStockStatus::Backorder->value => 'danger',
                    ProductStockStatus::Discontinued->value => 'default',
                ])
                ->labels(ProductStockStatus::options())
                ->sortable(),

            Select::make('Stock Status', 'stock_status')
                ->onlyOnForms()
                ->options(ProductStockStatus::options())
                ->displayUsingLabels()
                ->sortable(),

            Panel::make('Core Attributes', $this->coreAttributeFields()),

            Panel::make('External Attributes', $this->externalAttributeFields()),

        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            resolve(CleanUpStaleProducts::class)->standalone(),
        ];
    }

    /**
     * Get the fields for the core attributes panel.
     */
    public function coreAttributeFields(): array
    {
        return [
            Text::make('Vendor SKU', 'vendor_sku')
                ->required()
                ->sortable()
                ->showOnPreview(),

            Number::make('Increments', 'increments')
                ->sortable()
                ->default(1)
                ->min(1)
                ->step(1)
                ->hideFromIndex(),
        ];
    }

    /**
     * Get the fields for the external attributes panel.
     */
    public function externalAttributeFields(): array
    {
        return [
            Text::make('External ID', 'external_id')
                ->hideFromIndex(),

            Text::make('External URL', 'external_url')
                ->hideFromIndex(),
        ];
    }
}
